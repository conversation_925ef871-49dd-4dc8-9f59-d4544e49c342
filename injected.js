// 네이버 항공권 API 요청을 감지하는 주입 스크립트

(function() {
  'use strict';
  
  console.log('[INJECTED] 스크립트 시작 - ', window.location.href);
  
  // 기존 함수들을 저장
  const originalFetch = window.fetch;
  const originalXHR = window.XMLHttpRequest;
  
  // 스크립트가 이미 주입되었는지 확인
  if (window.naverFlightAPIDetector) {
    console.log('[INJECTED] 이미 주입된 스크립트 감지됨');
    return;
  }
  
  // 주입 플래그 설정
  window.naverFlightAPIDetector = true;
  
  // 모든 네트워크 요청을 로깅
  const logRequest = (method, url) => {
    // 모든 요청 로깅 (개발용) - 너무 많으면 주석 처리
    // console.log(`[INJECTED] ${method}:`, url);
    
    // airline-api 요청 특별 감지
    if (url.includes('airline-api.naver.com')) {
      console.log(`[INJECTED] 🎯 TARGET API 감지! ${method} ${url}`);
    }
    
    // 네이버 관련 API 요청 필터링
    if (url.includes('naver.com') && (
        url.includes('flight') || 
        url.includes('airline') || 
        url.includes('api') ||
        url.includes('graphql') ||
        url.includes('search')
    )) {
      console.log(`[INJECTED] 🛩️ 네이버 항공 관련 API 요청: ${method} ${url}`);
    }
  };
  
  // Fetch API 오버라이드
  window.fetch = function(url, options = {}) {
    const method = options.method || 'GET';
    logRequest(method, url);
    
    return originalFetch(url, options).then(response => {
      // 네이버 관련 API 응답 처리 (더 넓은 범위)
      if (url.includes('naver.com') && (
          url.includes('flight') || 
          url.includes('airline') || 
          url.includes('graphql') ||
          url.includes('api')
      )) {
        console.log('[INJECTED] 🛩️ 네이버 항공 관련 API 응답 처리 시작:', url);
        
        // 응답을 복제하여 원본을 보존
        const clonedResponse = response.clone();
        
        clonedResponse.text().then(text => {
          try {
            const data = JSON.parse(text);
            console.log('[INJECTED] 🛩️ API 응답 파싱 성공:', data);
            
            // GraphQL 응답인지 확인
            if (data?.data?.internationalList?.results?.fares || 
                data?.data?.domesticList?.results?.fares ||
                (data?.data && Object.keys(data.data).some(key => key.includes('List')))) {
              
              console.log('[INJECTED] ✈️ 항공편 데이터 응답 감지!');
              
              // postMessage로 content script에 전달
              window.postMessage({
                type: 'NAVER_FLIGHT_API_DATA',
                payload: {
                  url: url,
                  method: method,
                  requestData: options.body,
                  response: data,
                  timestamp: Date.now()
                }
              }, '*');
            }
            
          } catch (e) {
            console.error('[INJECTED] API 응답 파싱 실패:', e);
            console.log('[INJECTED] 원본 텍스트 (첫 500자):', text.substring(0, 500));
          }
        }).catch(e => {
          console.error('[INJECTED] 응답 텍스트 읽기 실패:', e);
        });
      }
      
      return response;
    }).catch(error => {
      console.error('[INJECTED] Fetch 에러:', error);
      throw error;
    });
  };
  
  // XMLHttpRequest 오버라이드
  function XHRProxy() {
    const xhr = new originalXHR();
    const originalOpen = xhr.open;
    const originalSend = xhr.send;
    
    xhr.open = function(method, url, ...args) {
      this._url = url;
      this._method = method;
      logRequest(method, url);
      return originalOpen.apply(this, [method, url, ...args]);
    };
    
    xhr.send = function(data) {
      const self = this;
      
      // 응답 처리
      this.addEventListener('load', function() {
        if (self._url && self._url.includes('naver.com') && (
            self._url.includes('flight') || 
            self._url.includes('airline') || 
            self._url.includes('graphql') ||
            self._url.includes('api')
        )) {
          console.log('[INJECTED] 🛩️ 네이버 항공 관련 XHR 응답 처리 시작:', self._url);
          
          try {
            const responseData = JSON.parse(this.responseText);
            console.log('[INJECTED] 🛩️ XHR API 응답 파싱 성공:', responseData);
            
            // GraphQL 응답인지 확인
            if (responseData?.data?.internationalList?.results?.fares || 
                responseData?.data?.domesticList?.results?.fares ||
                (responseData?.data && Object.keys(responseData.data).some(key => key.includes('List')))) {
              
              console.log('[INJECTED] ✈️ XHR 항공편 데이터 응답 감지!');
              
              window.postMessage({
                type: 'NAVER_FLIGHT_API_DATA',
                payload: {
                  url: self._url,
                  method: self._method,
                  requestData: data,
                  response: responseData,
                  timestamp: Date.now()
                }
              }, '*');
            }
            
          } catch (e) {
            console.error('[INJECTED] XHR API 응답 파싱 실패:', e);
            console.log('[INJECTED] XHR 원본 텍스트 (첫 500자):', this.responseText.substring(0, 500));
          }
        }
      });
      
      this.addEventListener('error', function() {
        console.error('[INJECTED] XHR 에러:', self._url);
      });
      
      return originalSend.apply(this, arguments);
    };
    
    return xhr;
  }
  
  // XMLHttpRequest 교체
  window.XMLHttpRequest = XHRProxy;
  
  // 네트워크 응답 캐시
  const responseCache = new Map();
  
  // Response 인터셉터 (다른 방법)
  const originalResponseArrayBuffer = Response.prototype.arrayBuffer;
  const originalResponseText = Response.prototype.text;
  const originalResponseJson = Response.prototype.json;
  
  Response.prototype.arrayBuffer = function() {
    const url = this.url;
    if (url && url.includes('airline-api.naver.com')) {
      console.log('[INJECTED] 🎯 Response.arrayBuffer 호출됨:', url);
    }
    return originalResponseArrayBuffer.apply(this, arguments);
  };
  
  Response.prototype.text = function() {
    const url = this.url;
    if (url && url.includes('airline-api.naver.com')) {
      console.log('[INJECTED] 🎯 Response.text 호출됨:', url);
      const promise = originalResponseText.apply(this, arguments);
      promise.then(text => {
        console.log('[INJECTED] 🎯 Response.text 결과:', text.substring(0, 500));
        try {
          const data = JSON.parse(text);
          if (data?.data?.internationalList?.results?.fares) {
            console.log('[INJECTED] ✈️ Response.text에서 항공편 데이터 감지!');
            window.postMessage({
              type: 'NAVER_FLIGHT_API_DATA',
              payload: {
                url: url,
                method: 'POST',
                response: data,
                timestamp: Date.now()
              }
            }, '*');
          }
        } catch (e) {
          console.log('[INJECTED] Response.text JSON 파싱 실패:', e);
        }
      });
      return promise;
    }
    return originalResponseText.apply(this, arguments);
  };
  
  Response.prototype.json = function() {
    const url = this.url;
    if (url && url.includes('airline-api.naver.com')) {
      console.log('[INJECTED] 🎯 Response.json 호출됨:', url);
      const promise = originalResponseJson.apply(this, arguments);
      promise.then(data => {
        console.log('[INJECTED] 🎯 Response.json 결과:', data);
        if (data?.data?.internationalList?.results?.fares) {
          console.log('[INJECTED] ✈️ Response.json에서 항공편 데이터 감지!');
          window.postMessage({
            type: 'NAVER_FLIGHT_API_DATA',
            payload: {
              url: url,
              method: 'POST',
              response: data,
              timestamp: Date.now()
            }
          }, '*');
        }
      });
      return promise;
    }
    return originalResponseJson.apply(this, arguments);
  };
  
  // Performance Observer로 네트워크 요청 감지 (백업 방법)
  if (window.PerformanceObserver) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('airline-api.naver.com')) {
          console.log('[INJECTED] 🔍 Performance Observer로 감지된 airline-api 요청:', entry.name);
          
          // 요청이 완료된 경우에만 처리
          if (entry.responseEnd > 0) {
            console.log('[INJECTED] 🔍 요청 완료됨, 응답 데이터 검색 시도');
            
            // 가능한 응답 데이터 검색 방법들을 시도
            setTimeout(() => {
              tryToGetResponseData(entry.name);
            }, 100);
          }
        }
      });
    });
    
    try {
      observer.observe({ entryTypes: ['resource'] });
      console.log('[INJECTED] Performance Observer 활성화됨');
    } catch (e) {
      console.log('[INJECTED] Performance Observer 사용 불가:', e);
    }
  }
  
  // 응답 데이터 검색 시도
  function tryToGetResponseData(url) {
    console.log('[INJECTED] 응답 데이터 검색 시도:', url);
    // 이 시점에서는 응답이 이미 처리되었을 수 있으므로
    // Response 인터셉터나 다른 방법으로 데이터를 얻어야 함
  }
  
  // 주기적으로 Performance entries 체크 (추가 백업)
  setInterval(() => {
    const entries = performance.getEntriesByType('resource');
    const airlineApiEntries = entries.filter(entry => 
      entry.name.includes('airline-api.naver.com') && 
      entry.responseEnd > performance.now() - 5000 // 최근 5초 내
    );
    
    if (airlineApiEntries.length > 0) {
      console.log('[INJECTED] 🔍 Performance entries에서 발견된 airline-api 요청들:', airlineApiEntries);
    }
  }, 2000);
  
  console.log('[INJECTED] API 감지 스크립트 로드 완료');
  
  // 디버깅을 위한 현재 오버라이드 상태 확인
  console.log('[INJECTED] 현재 fetch 함수:', window.fetch.toString().substring(0, 100));
  console.log('[INJECTED] 현재 XMLHttpRequest:', window.XMLHttpRequest.toString().substring(0, 100));
  
  // 테스트 메시지 전송 (5초 후)
  setTimeout(() => {
    console.log('[INJECTED] 테스트 메시지 전송');
    window.postMessage({
      type: 'NAVER_FLIGHT_API_DATA',
      payload: {
        url: 'https://airline-api.naver.com/graphql',
        method: 'TEST',
        response: { 
          test: true, 
          message: 'API 감지 스크립트가 정상적으로 로드되었습니다',
          timestamp: Date.now()
        },
        timestamp: Date.now()
      }
    }, '*');
  }, 5000);
  
})();