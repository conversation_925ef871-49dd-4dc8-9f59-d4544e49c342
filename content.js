// 네이버 항공권 API 요청을 감지하고 데이터를 시각화하는 컨텐츠 스크립트

class NaverFlightVisualizer {
  constructor() {
    this.apiData = [];
    this.isActive = false;
    this.chart = null;
    this.selectedAirlines = new Set();
    this.init();
  }

  init() {
    this.injectScript();
    this.setupMessageListener();
    this.createVisualizationPanel();
    this.setupToggleButton();
  }

  // API 요청을 감지하기 위한 스크립트 주입
  injectScript() {
    console.log('[CONTENT] 스크립트 주입 시작');
    
    // DOM이 준비될 때까지 기다린 후 주입
    const injectWhenReady = () => {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('injected.js');
      script.onload = () => {
        console.log('[CONTENT] injected.js 로드 완료');
        script.remove();
      };
      script.onerror = (error) => {
        console.error('[CONTENT] injected.js 로드 실패:', error);
      };
      
      const target = document.head || document.documentElement;
      if (target) {
        target.appendChild(script);
        console.log('[CONTENT] 스크립트 태그 추가됨');
      } else {
        console.error('[CONTENT] 주입할 타겟을 찾을 수 없음');
        // 1초 후 재시도
        setTimeout(injectWhenReady, 1000);
      }
    };
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', injectWhenReady);
    } else {
      injectWhenReady();
    }
  }

  // 주입된 스크립트로부터 메시지 수신
  setupMessageListener() {
    console.log('[CONTENT] 메시지 리스너 설정 중...');
    
    window.addEventListener('message', (event) => {
      // 소스 검증
      if (event.source !== window) return;
      
      console.log('[CONTENT] 메시지 수신:', event.data);
      
      if (event.data.type === 'NAVER_FLIGHT_API_DATA') {
        console.log('[CONTENT] GraphQL API 데이터 메시지 처리');
        this.handleApiData(event.data.payload);
      }
    });
    
    console.log('[CONTENT] 메시지 리스너 설정 완료');
  }

  // API 데이터 처리
  handleApiData(data) {
    console.log('네이버 항공권 GraphQL API 데이터:', data);
    
    // GraphQL getInternationalList 응답 처리
    if (data.response?.data?.internationalList?.results?.fares) {
      this.processInternationalFares(data.response.data.internationalList.results);
    }
  }

  // 여행사 코드 매핑
  getAgentName(agtCode) {
    const agentMap = {
      'INT005': 'NOL인터파크투어',
      'ONT003': '온라인투어',
      'HAT004': '하나투어',
      'MRT023': '마이리얼트립',
      'WEB001': '웹투어',
      'WHY002': '와이페이모어',
      'MOT007': '모두투어',
      'YEL009': '노랑풍선',
      'CTR013': '트립닷컴',
      'LTT017': '롯데관광',
      'KRT006': '여행이지',
      'TOV025': '투어비스',
      'NIT015': '내일투어',
      'HDT028': '더현대트래블',
    };
    return agentMap[agtCode] || agtCode;
  }

  // 카드 코드 매핑
  getCardName(cardCode) {
    const cardMap = {
      'B01': 'KB국민카드',
      'B02': '롯데카드',
      'B03': '신한카드 The CLASSIC+',
      'B04': 'BC카드',
      'B05': '삼성카드',
      'B06': '외환카드',
      'B07': '하나 트래블GO VISA 체크카드',
      'B08': '하나 트래블로그 SKYPASS',
      'B09': '현대카드',
      'B10': '하나 트래블로그 PRESTIGE',
      'B11': '씨티카드',
      'B12': '우리카드',
      'B13': 'NH채움카드',
      'B14': '롯데DIGILOCA SKYPASS(AMEX)',
      'B19': '신한카드',
      'B22': 'KB국민 AMEX카드',
      'B25': 'IBK기업은행카드',
      'B26': '하나/외환카드',
      'B27': '글로벌쇼핑 삼성카드',
      'B31': '하나카드',
      'B32': 'KB국민 청춘대로 티타늄카드',
      'B37': 'KB국민/씨티/NH채움카드',
      'B40': 'KB국민 비자카드',
      'B47': '하나멤버스 1Q 카드 Daily',
      'B50': 'KB국민 청춘대로 1코노미카드',
      'B51': '신한카드 Air 1.5',
      'B55': '하나멤버스 1Q Tour1 카드',
      'B56': '글로벌쇼핑 삼성카드 5 V2',
      'B57': '삼성카드 taptap I',
      'B59': 'SKYPASS THE DREAM 롯데카드',
      'B62': '하나 BC카드',
      'B65': '아메리칸 엑스프레스 그린',
      'B70': '유니온페이 카드',
      'B71': 'KB국민 톡톡PAY카드',
      'B72': 'NH농협카드',
      'B74': '네이버페이',
      'B77': 'BC바로카드',
      'B79': '현대 the Red Edition2(실적충족시)',
      'B80': '현대 M2/M3 Edition2(실적충족시)',
      'B81': '부산은행 BC카드',
      'B82': '대구은행 BC카드',
      'B83': '현대카드 the Pink',
      'B84': 'KB국민 WAVVE',
      'B85': 'KB국민 스카이패스 티타늄',
      'B86': 'KB국민 체크카드',
      'B87': '하나 트래블로그 신용카드',
      'B88': '삼성 iD PET 카드',
      'B89': '현대 AMEX 카드',
      'B90': 'KB국민 몰테일카드',
      'B91': 'KB국민카드(실적충족시)',
      'B92': '삼성 iD NOMAD 카드',
      'B93': '롯데 Trip to 로카 카드',
      'B94': '우리 카드의 정석 EVERY1',
      'B95': 'Mile1 하나카드',
      'B96': '삼성 iD GLOBAL 카드',
      'B97': 'KB국민 트래블러스 체크카드(Master)',
      'B98': 'JADE Classic 하나카드',
      'B99': '신한 SOL트래블 체크카드'
    };
    return cardMap[cardCode] || cardCode;
  }

  // 스케줄 키에서 항공사 코드 추출
  extractAirlineCodesFromScheduleKey(scheduleKey) {
    // 스케줄 키 형태: "구간1|구간2|..." 각 구간에서 항공사 코드 추출
    const segments = scheduleKey.split('|');
    const airlineCodes = new Set();
    
    segments.forEach(segment => {
      // 뒤에서 5~6번째 자리에서 항공사 코드 (2글자) + 편명 (4자리) 추출
      if (segment.length >= 6) {
        // 뒤에서 6자리를 가져와서 앞의 2자리가 항공사 코드
        const lastPart = segment.slice(-6);
        const airlineMatch = lastPart.match(/^[A-Z]{2}/);
        if (airlineMatch) {
          airlineCodes.add(airlineMatch[0]);
        }
      }
    });
    
    return Array.from(airlineCodes);
  }

  // 특정 항공사들이 모든 구간에 포함되는지 확인
  scheduleContainsAirlines(scheduleKey, targetAirlines) {
    const scheduleAirlines = this.extractAirlineCodesFromScheduleKey(scheduleKey);
    return targetAirlines.some(airline => scheduleAirlines.includes(airline));
  }

  // GraphQL internationalList 응답 처리
  processInternationalFares(results) {
    const { fares, schedules, airlines } = results;
    const processedData = [];
    
    // airlines 정보를 저장
    this.airlinesData = airlines || {};

    // fares 객체를 순회하며 각 항공편의 요금 정보 처리
    Object.entries(fares).forEach(([scheduleKey, fareData]) => {
      const { fare } = fareData;
      
      // 스케줄 키에서 항공사 코드들 추출
      const airlineCodes = this.extractAirlineCodesFromScheduleKey(scheduleKey);
      
      // 각 운임 타입별로 처리
      Object.entries(fare).forEach(([fareType, fareList]) => {
        fareList.forEach(fareItem => {
          const totalPrice = parseInt(fareItem.Adult?.Fare || 0) + 
                           parseInt(fareItem.Adult?.Tax || 0) + 
                           parseInt(fareItem.Adult?.QCharge || 0);
          
          if (totalPrice > 0) {
            // 카드 할인 정보 분석
            const isGeneralAdult = fareType === 'A01'; // 일반 성인 요금
            const isCardDiscount = fareType.includes('/B'); // 카드 할인 요금
            const cardCode = isCardDiscount ? fareType.split('/')[1] : null;
            
            processedData.push({
              scheduleKey,
              fareType,
              agent: this.getAgentName(fareItem.AgtCode),
              agentCode: fareItem.AgtCode,
              price: totalPrice,
              fare: parseInt(fareItem.Adult?.Fare || 0),
              tax: parseInt(fareItem.Adult?.Tax || 0),
              qcharge: parseInt(fareItem.Adult?.QCharge || 0),
              confirmType: fareItem.ConfirmType,
              baggageType: fareItem.BaggageType,
              isGeneralAdult,
              isCardDiscount,
              cardCode,
              timestamp: Date.now()
            });
          }
        });
      });
    });

    // 기존 데이터에 추가 (중복 제거)
    const newData = processedData.filter(newItem => 
      !this.apiData.some(existingItem => 
        existingItem.scheduleKey === newItem.scheduleKey &&
        existingItem.agentCode === newItem.agentCode &&
        existingItem.fareType === newItem.fareType
      )
    );

    this.apiData = [...this.apiData, ...newData];
    console.log(`새로운 요금 데이터 ${newData.length}개 추가됨. 총 ${this.apiData.length}개`);
    
    this.updateVisualization();
  }

  // 항공편 데이터 처리 (기존 코드 호환성 유지)
  processFlightData(flightList) {
    const processedData = flightList.map(flight => ({
      airline: flight.airlineList?.[0]?.name || 'Unknown',
      price: flight.fareList?.[0]?.totalFare || 0,
      duration: flight.totalTime || 0,
      stops: flight.stopCount || 0,
      departure: flight.departureTime,
      arrival: flight.arrivalTime
    }));
    
    this.apiData = [...this.apiData, ...processedData];
    this.updateVisualization();
  }

  // 가격 데이터 처리 (기존 코드 호환성 유지)
  processPriceData(priceData) {
    console.log('가격 데이터:', priceData);
    this.updatePriceChart(priceData);
  }

  // 시각화 패널 생성
  createVisualizationPanel() {
    const panel = document.createElement('div');
    panel.id = 'naver-flight-viz-panel';
    panel.innerHTML = `
      <div class="viz-header">
        <h3>카드 할인율 분석</h3>
        <button id="viz-close">×</button>
      </div>
      <div class="viz-content">
        <div id="airline-filter">
          <h4>항공사 필터</h4>
          <div id="airline-tags"></div>
        </div>
        <div id="card-agent-chart">
          <h4>카드별 여행사 할인율 비교</h4>
          <div id="card-agent-bars"></div>
        </div>
      </div>
    `;
    
    document.body.appendChild(panel);
    
    // 닫기 버튼 이벤트
    document.getElementById('viz-close').addEventListener('click', () => {
      this.togglePanel(false);
    });
  }

  // 토글 버튼 생성
  setupToggleButton() {
    const button = document.createElement('button');
    button.id = 'naver-flight-viz-toggle';
    button.innerHTML = '📊';
    button.title = '항공권 데이터 시각화 토글';
    
    button.addEventListener('click', () => {
      this.togglePanel();
    });
    
    document.body.appendChild(button);
  }

  // 패널 토글
  togglePanel(force = null) {
    const panel = document.getElementById('naver-flight-viz-panel');
    this.isActive = force !== null ? force : !this.isActive;
    panel.style.display = this.isActive ? 'block' : 'none';
    
    if (this.isActive) {
      this.updateVisualization();
    }
  }

  // 시각화 업데이트
  updateVisualization() {
    if (!this.isActive) return;
    
    this.updateAirlineFilter();
    this.updateCardAgentChart();
  }

  // 항공사 필터 업데이트
  updateAirlineFilter() {
    const container = document.getElementById('airline-tags');
    
    // fares 키에서 항공사 코드 추출
    const availableAirlines = new Set();
    
    // 먼저 airlines 데이터에서 모든 항공사 추출
    if (this.airlinesData && typeof this.airlinesData === 'object') {
      Object.keys(this.airlinesData).forEach(code => {
        availableAirlines.add(code);
      });
    }
    
    // API 데이터에서 스케줄 키를 파싱해서 항공사 코드 추출
    this.apiData.forEach(fare => {
      if (fare.scheduleKey) {
        const airlineCodes = this.extractAirlineCodesFromScheduleKey(fare.scheduleKey);
        airlineCodes.forEach(code => availableAirlines.add(code));
      }
    });
    
    console.log('Available airlines:', Array.from(availableAirlines));
    console.log('Airlines data:', this.airlinesData);
    
    // 항공사 태그 생성
    container.innerHTML = '';
    if (availableAirlines.size === 0) {
      container.innerHTML = '<p class="no-airlines">항공사 데이터가 없습니다.</p>';
      return;
    }
    
    // 전체 선택/해제 버튼
    const allButton = document.createElement('button');
    allButton.className = 'airline-tag all-tag';
    allButton.textContent = this.selectedAirlines.size === 0 ? '전체 선택' : '전체 해제';
    allButton.addEventListener('click', () => {
      if (this.selectedAirlines.size === 0) {
        // 전체 선택
        availableAirlines.forEach(code => this.selectedAirlines.add(code));
      } else {
        // 전체 해제
        this.selectedAirlines.clear();
      }
      this.updateVisualization();
    });
    container.appendChild(allButton);
    
    // 각 항공사별 태그 (airlines 정보 활용)
    Array.from(availableAirlines).sort().forEach(airlineCode => {
      const tag = document.createElement('button');
      tag.className = `airline-tag ${this.selectedAirlines.has(airlineCode) ? 'selected' : ''}`;
      
      // airlines 데이터에서 항공사명 가져오기
      let airlineName = airlineCode;
      if (this.airlinesData && this.airlinesData[airlineCode]) {
        const airlineInfo = this.airlinesData[airlineCode];
        if (typeof airlineInfo === 'object' && airlineInfo.name) {
          airlineName = airlineInfo.name;
        } else if (typeof airlineInfo === 'string') {
          airlineName = airlineInfo;
        }
      }
      
      tag.textContent = airlineName !== airlineCode ? `${airlineCode} (${airlineName})` : airlineCode;
      tag.title = airlineName;
      
      tag.addEventListener('click', () => {
        if (this.selectedAirlines.has(airlineCode)) {
          this.selectedAirlines.delete(airlineCode);
        } else {
          this.selectedAirlines.add(airlineCode);
        }
        this.updateVisualization();
      });
      container.appendChild(tag);
    });
  }

  // 할인율 계산 (항공사 필터 적용)
  calculateDiscountRates() {
    const discountRates = [];
    
    // 항공사 필터 적용
    const filteredData = this.selectedAirlines.size === 0 ? 
      this.apiData : 
      this.apiData.filter(fare => {
        // 스케줄 키에서 항공사 코드를 추출해서 선택된 항공사와 비교
        if (fare.scheduleKey) {
          const airlineCodes = this.extractAirlineCodesFromScheduleKey(fare.scheduleKey);
          return airlineCodes.some(code => this.selectedAirlines.has(code));
        }
        return false;
      });
    
    // 스케줄별, 여행사별로 그룹화
    const groupedData = {};
    
    filteredData.forEach(fare => {
      const key = `${fare.scheduleKey}_${fare.agentCode}`;
      if (!groupedData[key]) {
        groupedData[key] = { general: null, cards: [] };
      }
      
      if (fare.isGeneralAdult) {
        groupedData[key].general = fare;
      } else if (fare.isCardDiscount) {
        groupedData[key].cards.push(fare);
      }
    });
    
    // 할인율 계산
    Object.values(groupedData).forEach(group => {
      if (group.general && group.cards.length > 0) {
        group.cards.forEach(cardFare => {
          const discountRate = ((group.general.price - cardFare.price) / group.general.price) * 100;
          if (discountRate > 0) {
            discountRates.push({
              discountRate,
              cardCode: cardFare.cardCode,
              agent: cardFare.agent,
              generalPrice: group.general.price,
              cardPrice: cardFare.price,
              scheduleKey: cardFare.scheduleKey
            });
          }
        });
      }
    });
    
    return discountRates;
  }

  // 카드별 여행사 할인율 매트릭스 차트 업데이트
  updateCardAgentChart() {
    const container = document.getElementById('card-agent-bars');
    container.innerHTML = '';
    
    // 카드 매핑 정보 사용
    const cardMap = {
      'B01': 'KB국민카드',
      'B02': '롯데카드',
      'B03': '신한카드 The CLASSIC+',
      'B04': 'BC카드',
      'B05': '삼성카드',
      'B06': '외환카드',
      'B07': '하나 트래블GO VISA 체크카드',
      'B08': '하나 트래블로그 SKYPASS',
      'B09': '현대카드',
      'B10': '하나 트래블로그 PRESTIGE',
      'B11': '씨티카드',
      'B12': '우리카드',
      'B13': 'NH채움카드',
      'B14': '롯데DIGILOCA SKYPASS(AMEX)',
      'B19': '신한카드',
      'B22': 'KB국민 AMEX카드',
      'B25': 'IBK기업은행카드',
      'B26': '하나/외환카드',
      'B27': '글로벌쇼핑 삼성카드',
      'B31': '하나카드',
      'B32': 'KB국민 청춘대로 티타늄카드',
      'B37': 'KB국민/씨티/NH채움카드',
      'B40': 'KB국민 비자카드',
      'B47': '하나멤버스 1Q 카드 Daily',
      'B50': 'KB국민 청춘대로 1코노미카드',
      'B51': '신한카드 Air 1.5',
      'B55': '하나멤버스 1Q Tour1 카드',
      'B56': '글로벌쇼핑 삼성카드 5 V2',
      'B57': '삼성카드 taptap I',
      'B59': 'SKYPASS THE DREAM 롯데카드',
      'B62': '하나 BC카드',
      'B65': '아메리칸 엑스프레스 그린',
      'B70': '유니온페이 카드',
      'B71': 'KB국민 톡톡PAY카드',
      'B72': 'NH농협카드',
      'B74': '네이버페이',
      'B77': 'BC바로카드',
      'B79': '현대 the Red Edition2(실적충족시)',
      'B80': '현대 M2/M3 Edition2(실적충족시)',
      'B81': '부산은행 BC카드',
      'B82': '대구은행 BC카드',
      'B83': '현대카드 the Pink',
      'B84': 'KB국민 WAVVE',
      'B85': 'KB국민 스카이패스 티타늄',
      'B86': 'KB국민 체크카드',
      'B87': '하나 트래블로그 신용카드',
      'B88': '삼성 iD PET 카드',
      'B89': '현대 AMEX 카드',
      'B90': 'KB국민 몰테일카드',
      'B91': 'KB국민카드(실적충족시)',
      'B92': '삼성 iD NOMAD 카드',
      'B93': '롯데 Trip to 로카 카드',
      'B94': '우리 카드의 정석 EVERY1',
      'B95': 'Mile1 하나카드',
      'B96': '삼성 iD GLOBAL 카드',
      'B97': 'KB국민 트래블러스 체크카드(Master)',
      'B98': 'JADE Classic 하나카드',
      'B99': '신한 SOL트래블 체크카드'
    };
    
    const discountRates = this.calculateDiscountRates();
    
    // 카드별 여행사별 할인율 매트릭스 생성
    const cardAgentMatrix = {};
    discountRates.forEach(rate => {
      if (!cardAgentMatrix[rate.cardCode]) {
        cardAgentMatrix[rate.cardCode] = {};
      }
      if (!cardAgentMatrix[rate.cardCode][rate.agent]) {
        cardAgentMatrix[rate.cardCode][rate.agent] = [];
      }
      cardAgentMatrix[rate.cardCode][rate.agent].push(rate.discountRate);
    });
    
    // 전체 최대 할인율 계산 (스케일링용)
    let globalMaxDiscount = 0;
    Object.values(cardAgentMatrix).forEach(agents => {
      if (agents && typeof agents === 'object') {
        Object.values(agents).forEach(rates => {
          if (Array.isArray(rates) && rates.length > 0) {
            const avgDiscount = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
            if (avgDiscount > globalMaxDiscount) {
              globalMaxDiscount = avgDiscount;
            }
          }
        });
      }
    });
    
    // 모든 카드에 대해 섹션 생성
    Object.entries(cardMap).forEach(([cardCode, cardName]) => {
      const cardSection = document.createElement('div');
      cardSection.className = 'card-section';
      
      const cardHeader = document.createElement('div');
      cardHeader.className = 'card-header';
      cardHeader.style.cursor = 'pointer';
      
      const agents = cardAgentMatrix[cardCode];
      const hasData = agents && Object.keys(agents).length > 0;
      
      let maxDiscount = 0;
      let agentCount = 0;
      if (hasData) {
        const agentDiscounts = Object.values(agents)
          .filter(rates => Array.isArray(rates) && rates.length > 0)
          .map(rates => rates.reduce((sum, rate) => sum + rate, 0) / rates.length);
        maxDiscount = agentDiscounts.length > 0 ? Math.max(...agentDiscounts) : 0;
        agentCount = Object.keys(agents).length;
      }
      
      cardHeader.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
          <span class="toggle-icon">${hasData ? '▼' : '▶'}</span>
          <span class="card-name">${cardName}</span>
        </div>
        <span class="max-discount">${hasData ? `최대 ${maxDiscount.toFixed(1)}% (${agentCount}개사)` : '데이터 없음'}</span>
      `;
      
      const agentBars = document.createElement('div');
      agentBars.className = 'agent-bars';
      agentBars.style.display = 'none'; // 기본적으로 접힘
      
      if (hasData) {
        const agentDiscounts = Object.entries(agents).map(([agent, rates]) => ({
          agent,
          avgDiscount: rates.reduce((sum, rate) => sum + rate, 0) / rates.length,
          count: rates.length
        })).sort((a, b) => b.avgDiscount - a.avgDiscount);
        
        agentDiscounts.forEach(agentData => {
          const agentBar = document.createElement('div');
          agentBar.className = 'agent-bar';
          agentBar.innerHTML = `
            <div class="agent-info">
              <span class="agent-name">${agentData.agent}</span>
              <span class="agent-discount">${agentData.avgDiscount.toFixed(1)}%</span>
            </div>
            <div class="agent-bar-fill" style="width: ${globalMaxDiscount > 0 ? (agentData.avgDiscount / globalMaxDiscount) * 100 : 0}%"></div>
            <span class="agent-count">${agentData.count}건</span>
          `;
          agentBars.appendChild(agentBar);
        });
      } else {
        agentBars.innerHTML = '<div class="no-data">할인 데이터가 없습니다.</div>';
      }
      
      // 클릭 이벤트로 접기/펼치기
      cardHeader.addEventListener('click', () => {
        const isVisible = agentBars.style.display !== 'none';
        agentBars.style.display = isVisible ? 'none' : 'block';
        const toggleIcon = cardHeader.querySelector('.toggle-icon');
        toggleIcon.textContent = isVisible ? '▶' : '▼';
      });
      
      cardSection.appendChild(cardHeader);
      cardSection.appendChild(agentBars);
      container.appendChild(cardSection);
    });
  }
}

// 페이지 로드 완료 후 초기화
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new NaverFlightVisualizer();
  });
} else {
  new NaverFlightVisualizer();
}