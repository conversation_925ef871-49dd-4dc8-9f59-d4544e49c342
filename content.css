/* 네이버 항공권 데이터 시각화 스타일 */

#naver-flight-viz-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: #4285f4;
  color: white;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

#naver-flight-viz-toggle:hover {
  background: #3367d6;
  transform: scale(1.1);
}

#naver-flight-viz-panel {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 1000px;
  max-width: 90vw;
  max-height: 700px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  z-index: 9999;
  display: none;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
}

.viz-header {
  background: #4285f4;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.viz-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

#viz-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

#viz-close:hover {
  background: rgba(255,255,255,0.2);
}

.viz-content {
  display: flex;
  height: 620px;
  overflow: hidden;
}

.viz-left-panel {
  width: 40%;
  border-right: 1px solid #e0e0e0;
  padding: 20px;
  overflow-y: auto;
  background: #f8f9fa;
}

.viz-right-panel {
  width: 60%;
  padding: 20px;
  overflow-y: auto;
  background: white;
}

.viz-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.viz-charts {
  margin-top: 20px;
}

#price-chart {
  width: 100%;
  height: 200px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 20px;
}

#card-agent-chart {
  margin-bottom: 25px;
}

#card-agent-chart h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 카드별 여행사 할인율 매트릭스 차트 */
.card-section {
  margin-bottom: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.card-header:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.toggle-icon {
  font-size: 12px;
  color: white;
  transition: transform 0.2s ease;
}

.card-header .card-name {
  font-size: 13px;
  font-weight: 600;
  color: white;
}

.max-discount {
  font-size: 12px;
  font-weight: 500;
  background: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 12px;
}

.agent-bars {
  padding: 12px;
  background: #f8f9fa;
}

.agent-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
  padding: 6px 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.agent-bar:last-child {
  margin-bottom: 0;
}

.agent-info {
  display: flex;
  flex-direction: column;
  min-width: 90px;
}

.agent-name {
  font-size: 11px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.agent-discount {
  font-size: 10px;
  color: #e74c3c;
  font-weight: 600;
  margin-top: 1px;
}

.agent-bar-fill {
  background: linear-gradient(90deg, #ff6b6b, #ee5a24);
  height: 12px;
  border-radius: 6px;
  min-width: 3px;
  transition: width 0.3s ease;
  flex-grow: 1;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.agent-count {
  font-size: 10px;
  color: #666;
  min-width: 30px;
  text-align: right;
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 10px;
}

.no-data {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 16px;
  font-style: italic;
}

/* 스케줄 목록 스타일 */
#schedule-list {
  margin-top: 20px;
}

#schedule-list h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.schedule-item {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.schedule-item:hover {
  border-color: #4285f4;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
}

.schedule-item.selected {
  border-color: #4285f4;
  background: #e8f0fe;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.schedule-airlines {
  font-size: 12px;
  font-weight: 600;
  color: #4285f4;
}

.schedule-price-range {
  font-size: 11px;
  color: #666;
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 10px;
}

.schedule-details {
  font-size: 11px;
  color: #666;
  line-height: 1.4;
}

.schedule-stats {
  display: flex;
  gap: 10px;
  margin-top: 6px;
}

.schedule-stat {
  font-size: 10px;
  color: #888;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 항공사 필터 스타일 */
#airline-filter {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

#airline-filter h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

#airline-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.airline-tag {
  padding: 4px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 16px;
  background: #f8f9fa;
  color: #666;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
}

.airline-tag:hover {
  background: #e9ecef;
  border-color: #b0b0b0;
}

.airline-tag.selected {
  background: #4285f4;
  color: white;
  border-color: #4285f4;
}

.airline-tag.all-tag {
  background: #28a745;
  color: white;
  border-color: #28a745;
  font-weight: 600;
}

.airline-tag.all-tag:hover {
  background: #218838;
  border-color: #1e7e34;
}

.no-airlines {
  color: #999;
  font-size: 12px;
  font-style: italic;
  margin: 0;
}

/* 기존 항공사 차트 (호환성 유지) */
#airline-chart h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.airline-bar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.airline-name {
  min-width: 80px;
  font-size: 12px;
  color: #666;
}

.airline-bar-fill {
  background: linear-gradient(90deg, #4285f4, #34a853);
  height: 20px;
  border-radius: 10px;
  min-width: 4px;
  transition: width 0.3s ease;
}

.airline-count {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  min-width: 20px;
  text-align: right;
}

/* 반응형 디자인 */
@media (max-width: 1200px) {
  #naver-flight-viz-panel {
    width: 95vw;
    max-width: 95vw;
  }
}

@media (max-width: 768px) {
  #naver-flight-viz-panel {
    width: 95vw;
    max-width: 95vw;
    top: 70px;
    max-height: 80vh;
  }

  .viz-content {
    flex-direction: column;
    height: auto;
    max-height: calc(80vh - 60px);
  }

  .viz-left-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
    max-height: 300px;
  }

  .viz-right-panel {
    width: 100%;
    max-height: 400px;
  }

  #naver-flight-viz-toggle {
    right: 15px;
    top: 15px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}

/* 스크롤바 스타일링 */
.viz-content::-webkit-scrollbar {
  width: 6px;
}

.viz-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.viz-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.viz-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}